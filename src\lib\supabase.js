import { createClient } from '@supabase/supabase-js';
import NetInfo from '@react-native-community/netinfo';
import { Alert } from 'react-native';
import Constants from 'expo-constants';

// Get Supabase configuration from environment variables
const SUPABASE_URL = Constants.expoConfig?.extra?.EXPO_PUBLIC_SUPABASE_URL || process.env.EXPO_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = Constants.expoConfig?.extra?.EXPO_PUBLIC_SUPABASE_ANON_KEY || process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error('Supabase environment variables missing:');
  console.error('SUPABASE_URL:', SUPABASE_URL ? 'Present' : 'Missing');
  console.error('SUPABASE_ANON_KEY:', SUPABASE_ANON_KEY ? 'Present' : 'Missing');
  throw new Error('Missing Supabase environment variables');
}

console.log('Initializing Supabase with URL:', SUPABASE_URL);

export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    persistSession: false
  }
});

// Verify Supabase connection
const verifyConnection = async () => {
  try {
    const { data, error } = await supabase.from('phq9_form').select('count').limit(1);
    if (error) {
      console.error('Supabase connection error:', error);
      return false;
    }
    console.log('Supabase connection successful');
    return true;
  } catch (error) {
    console.error('Supabase connection verification failed:', error);
    return false;
  }
};

// Call verification on initialization
verifyConnection();

// Helper function to check network connectivity
export const checkNetworkConnection = async () => {
  const netInfo = await NetInfo.fetch();
  return netInfo.isConnected;
};

// Helper function to handle Supabase operations with network check
export const supabaseOperation = async (operation) => {
  try {
    const isConnected = await checkNetworkConnection();
    if (!isConnected) {
      throw new Error('No internet connection. Please check your network and try again.');
    }

    // Verify Supabase connection before operation
    const isSupabaseConnected = await verifyConnection();
    if (!isSupabaseConnected) {
      throw new Error('Unable to connect to the database. Please try again later.');
    }

    return await operation();
  } catch (error) {
    console.error('Supabase operation error:', error);
    if (error.message.includes('Network request failed')) {
      throw new Error('Network request failed. Please check your internet connection and try again.');
    }
    throw error;
  }
}; 